<?php

namespace models\composite\oUserAccess;

use models\composite\oUser\getUserInfo;
use models\types\strongType;

class getAutomationControlAccess extends strongType
{
    /**
     * @param $param |userNumber
     * @return int|null
     */
    public static function getReport($param): ?int
    {
        if (!$param) {
            return null;
        }
        $params = [
            'UType' => 'Employee',
            'UID' => $param,
        ];
        $result = getUserInfo::getReport($params);
        foreach ($result as $userKey => $userValue) {
            $allowToViewAutomationPopup = $userValue['allowToViewAutomationPopup'];
            $allowToControlAutomationPopup = $userValue['allowToControlAutomationPopup'];
        }
        if ($allowToViewAutomationPopup == 1 && $allowToControlAutomationPopup == 1) {
            return 1;
        }
        return 0;
    }
}
