<?php

use models\composite\oAutomatedActions\getAutomatedActionScheduleTimeStamp;
use models\composite\oAutomatedActions\getAutomatedActionTitle;
use models\composite\oAutomatedActions\getAutomatedEmail;
use models\composite\oAutomatedActions\getAutomatedEmailRole;
use models\composite\oAutomatedActions\getAutomatedEmailSender;
use models\composite\oAutomatedActions\getAutomatedEmailUser;
use models\composite\oAutomatedActions\getAutomatedTask;
use models\composite\oAutomatedActions\getAutomatedTaskCreatedBy;
use models\composite\oAutomatedActions\getAutomatedTaskRole;
use models\composite\oAutomatedActions\getAutomatedWebhook;
use models\composite\oAutomatedActions\getReferralDateLoanFile;
use models\composite\oAutomatedActions\getTaskDueDate;
use models\composite\oAutomatedActions\getTaskRemainderDate;
use models\composite\oCustomDocs\getAllDynamicDatas;
use models\composite\oCustomDocs\SubstituteDynamicTagsForEmail;
use models\composite\oFile\getFileInfo;
use models\composite\oPC\getPCModules;

use models\composite\oUser\getUserInfo;
use models\composite\oUserAccess\getAutomationControlAccess;
use models\constants\automationConstants;
use models\constants\gl\glEventReferralDateArray;
use models\Controllers\backoffice\LMRequest;
use models\cypher;
use models\PageVariables;
use models\Log;
use models\Request;
use models\standard\Dates;
use models\standard\UserAccess;


$requestUrl = $_SERVER['REQUEST_URI'] ?? '';
$filename = basename($requestUrl);

if ($filename === 'automatedActionView') { //MVC code format
    require_once CONST_PATH . 'includes/util.php';
    require_once CONST_PATH . 'backoffice/initPageVariables.php';
    require_once CONST_PATH . 'backoffice/getPageVariables.php';
} else { // old code procedure format
    session_start();
    require_once '../includes/util.php';
    require_once 'initPageVariables.php';
    require_once 'getPageVariables.php';
}
//load models here
global $PCID, $userGroup, $userTimeZone;
UserAccess::checkReferrerPgs(['url' => 'LMRequest.php, myPipeline.php, adminWelcome.php, dashboard']);
UserAccess::CheckAdminUse();

$userControl = getAutomationControlAccess::getReport(PageVariables::$userNumber);
if ($userGroup != 'Employee') {
    $userControl = 0;
}
//fix for old reference date values
//based on the pc file modules
//variables
$modulesArray = [];
$assignedPCID = $PCID;
$ip = ['PCID' => $assignedPCID];
$ip['keyNeeded'] = 'n';
if ($assignedPCID > 0) {
    $ip = ['PCID' => $assignedPCID];
    $ip['keyNeeded'] = 'n';
    $modulesArray = getPCModules::getReport($ip);
}
$fileModules = [];
for ($j = 0; $j < count($modulesArray); $j++) {
    $fileModules[] = $modulesArray[$j]['moduleCode'];
}
//fix for old reference date values
//based on the pc file modules
$RDFT = '';
if (in_array('HMLO', $fileModules)) {
    $RDFT = 'HMLO-';
}
if (in_array('LM', $fileModules) || in_array('SS', $fileModules)) {
    $RDFT = 'LMSS-';
}
if (in_array('loc', $fileModules)) {
    $RDFT = 'LOC-';
}

$glEventReferralDateArray = array_merge(
    glEventReferralDateArray::$glEventReferralDateArray,
    glEventReferralDateArray::$glGeneralDates,
    glEventReferralDateArray::$glHMLODates,
    glEventReferralDateArray::$glLMSSDates,
    glEventReferralDateArray::$glBusinessFundingDates
);
$glEventReferralDateAllFileTypeArray = array_merge(
    glEventReferralDateArray::$glEventReferralDateArray,
    glEventReferralDateArray::$glGeneralDates
);
$glEventReferralDateAllFileTypeArrayKeys = array_keys($glEventReferralDateAllFileTypeArray);

$dateTimeNow = Dates::Timestamp();

$layout = '';
$showTaskTable = 'no';
$showEmailTable = 'no';
$showWebhookTable = 'no';
$showEmployeeTable = 'no';
$showChangeFileStatusTable = 'no';
$missingInfo = 0;
$taskRows = '';
$taskSchedule = ' --- ';
$emailRows = '';
$emailSchedule = ' --- ';
$webhookRows = '';
$webhookSchedule = ' --- ';
$employeeRows = '';
$changeFileStatusRows = '';
$cancelAction = intval(Request::GetClean('cancelAction')) ?? 0;
$postData = Request::GetClean('postData') ?? '';   // TODO - do not put htmlspecialchars here it is an array of crap
$LMRId = intval(cypher::myDecryption(Request::GetClean('LMRId')));

$LMRArray = getFileInfo::getReport(['LMRId' => $LMRId]);
$myFileInfo = $LMRArray[$LMRId];
$data = [];
foreach ($postData as $actionData) {
    $data[] = [
        'actionName' => htmlspecialchars($actionData['actionName']),
        'actionId'   => htmlspecialchars($actionData['actionId']),
    ];
}
if (count($data) > 0) { // Task / Email / Webhook - Instant / Schedule
    $t = 1;
    $e = 1;
    $w = 1;
    $emp = 1;
    $cfs = 1;
    $_customTempInfoArray_cache = [];
    foreach ($data as $action) {
        $infoArray = [];
        $scheduleOn = '';
        $actionName = $action['actionName'];
        if ($actionName == automationConstants::$automation_Task) {
            $showTaskTable = 'yes';
            $taskId = $action['actionId'];
            //get the task Details
            $tsparams = ['PCID' => $PCID, 'id' => $taskId];
            $automatedTask = getAutomatedTask::getReport($tsparams);
            $taskName = $automatedTask['taskSubject'];
            //Replace Merge Tags in the Task Subject and Body
            if (strpos($taskName, '##') !== false) {
                if(!isset($_customTempInfoArray_cache[$LMRId])) {
                    Log::Insert('getAllDynamicDatas::getReport - ' . $LMRId);
                    $_customTempInfoArray_cache[$LMRId] = getAllDynamicDatas::getReport(['LMRID' => $LMRId]);
                }
                $customTempInfoArray = $_customTempInfoArray_cache[$LMRId];
                if (strpos($taskName, '##') !== false) {
                    $customTempInfoArray['DOCCONTENT'] = $taskName;
                    $customTempInfoArray['LMRID'] = $LMRId;
                    $taskName = SubstituteDynamicTagsForEmail::getReport($customTempInfoArray);
                    $taskName = str_replace('<b>', '', $taskName);
                    $taskName = str_replace('</b>', '', $taskName);
                }
            }
            $taskType = $automatedTask['taskType'];
            //Task Created By
            $createdByParams = [
                'LMRId' => $LMRId,
                'PCID' => $PCID
            ];
            //Users
            //Task Created By//
            $taskFromRole = $automatedTask['taskFromRole'];
            if ($taskFromRole) {
                $createdByParams['createdBy'] = 'Role';
                $createdByParams['param'] = $taskFromRole;
                $taskFromRoleUI = $taskFromRole;
                if ($taskFromRole == 'Agent') $taskFromRoleUI = 'Broker';
                if ($taskFromRole == 'secondaryAgent') $taskFromRoleUI = 'Loan Officer';
                $missingCreatedBy = $taskFromRoleUI;
            }
            $taskFromEmail = $automatedTask['taskFromEmail'];
            if ($taskFromEmail) {
                $createdByParams['createdBy'] = 'User';
                $createdByParams['param'] = $taskFromEmail;
                $missingCreatedBy = 'User';
            }
            $taskCreatedByDetails = getAutomatedTaskCreatedBy::getReport($createdByParams, $myFileInfo);
            //pr($taskCreatedByDetails);
            if ($taskCreatedByDetails['fromName']) {
                $taskCreatedBy = $taskCreatedByDetails['fromName'] . '[' . $taskCreatedByDetails['Role'] . ']';
            } else {
                $taskCreatedBy = '<span class="text-danger">[Missing info for ' . $missingCreatedBy . ']</span>';
                $missingInfo = 1;
            }
            //Task Assigned To//
            $AssignedUsers = '';
            //Roles
            $taskAssignedRoles = getAutomatedTaskRole::getReport($tsparams);
            if (count($taskAssignedRoles) > 0) {
                $taskAssignedRoleData = [];
                foreach ($taskAssignedRoles as $taskAssignedRole) {
                    $roleName = $taskAssignedRole['roleName'];
                    $taskAssignedRoleParams = [
                        'PCID' => $PCID,
                        'LMRId' => $LMRId,
                        'param' => $roleName,
                        'createdBy' => 'Role',
                    ];
                    //pr($taskAssignedRoleParams);
                    $taskAssignedRoleDetails = getAutomatedTaskCreatedBy::getReport($taskAssignedRoleParams, $myFileInfo);
                    //pr($taskAssignedRoleDetails);
                    $taName = $taskAssignedRoleDetails['fromName']; //Task Assigned To Name
                    $taEmail = $taskAssignedRoleDetails['fromEmail']; //Task Assigned To Email
                    $roleNameUI = $roleName;
                    if ($roleName == 'Agent') $roleNameUI = 'Broker';
                    if ($roleName == 'secondaryAgent') $roleNameUI = 'Loan Officer';
                    $taData = $taName . ' (' . $roleNameUI . ')';
                    if ($taName == '' && $taEmail == '') {
                        $taData = '<span class="text-danger">[Missing info for ' . $roleNameUI . ']</span>';
                        $missingInfo = 1;
                    }
                    $taskAssignedRoleData[] = $taData;
                }
                $AssignedUsers = implode(',', $taskAssignedRoleData);
            }

            //Dates
            $noOfDaysDD = $eventWhenDD = '';
            $noOfDaysRD = $eventWhenRD = '';
            $noOfDaysDD = $automatedTask['noOfDaysDD'];
            $eventWhenDD = $automatedTask['eventWhenDD'];
            $noOfDaysRD = $automatedTask['noOfDaysRD'];
            $eventWhenRD = $automatedTask['eventWhenRD'];
            $reminderEmail = $automatedTask['reminderEmail'];
            $taskCreatedDate = Dates::Timestamp(); //Instant Task (current datetime)
            $taskTriggerDate = date('m/d/Y', strtotime($taskCreatedDate));
            if ($taskType == 'Schedule') $taskType = 'Scheduled'; //past tense
            if ($taskType == 'Scheduled') {
                $day = $automatedTask['noOfDays'] == 1 ? ' day ' : ' day(s)';
                $noOfDays = $automatedTask['noOfDays'];
                $eventWhen = $automatedTask['eventWhen'];
                if ($automatedTask['eventWhen'] == 'ON') {
                    $day = '';
                    $noOfDays = ' ON - ';
                    $eventWhen = '';
                }
                $taskReferralDateValue = $automatedTask['referralDate'];
                if ($taskReferralDateValue) {
                    $taskReferralDateExplode = explode('-', $taskReferralDateValue);
                    $taskReferralDateCount = count($taskReferralDateExplode);
                    if ($taskReferralDateCount == 1 &&
                        !in_array($taskReferralDateValue, $glEventReferralDateAllFileTypeArrayKeys)) { // old value - append file type
                        $taskReferralDateValue = $RDFT . $taskReferralDateValue;
                    }
                }
                $taskReferralDate = $taskReferralDateValue != '' ? $glEventReferralDateArray[$taskReferralDateValue] : '';
                if ($automatedTask['eventWhen'] == 'ON') {
                    $taskSchedule = 'ON , ' . $taskReferralDate;
                } else {
                    $taskSchedule = $noOfDays . $day . ', ' . strtolower($eventWhen) . ', ' . $taskReferralDate;
                }
                //get the referral Date from the Loan file
                $tsReferralDateLoanFile = getReferralDateLoanFile::getReport($taskReferralDateValue, $myFileInfo);
                if ($tsReferralDateLoanFile) {
                    $schParams = [
                        'noOfDays' => $automatedTask['noOfDays'],
                        'eventWhen' => $automatedTask['eventWhen'],
                        'referralDate' => $tsReferralDateLoanFile,
                        'userTimeZone' => $userTimeZone
                    ];
                    $tsScheduleTimeStampData = getAutomatedActionScheduleTimeStamp::getReport($schParams);
                    if ($tsScheduleTimeStampData) {
                        $tsSchDateTimeStampDe = json_decode($tsScheduleTimeStampData);
                        $tsScheduleDate = $tsSchDateTimeStampDe->scheduleDate;
                        $tsScheduleTimeStamp = $tsSchDateTimeStampDe->scheduleTimeStamp;
                    }
                }

                if ($taskReferralDate == 'Rule conditions are met') $taskReferralDate = 'Workflow activation ON ';
                if ($tsScheduleDate) {
                    $taskTriggerDate = date('m/d/Y', strtotime($tsScheduleDate));
                } else {
                    $taskTriggerDate = ' [Missing Information for ' . $taskReferralDate . ']';
                    $missingInfo = 1;
                }
                $taskCreatedDate = $tsScheduleDate;  //Schedule Task (Schedule datetime)
            }
            //Task Due Date
            $taskDueDate = '';
            $taskDueDateUI = '';
            $tddParams = [
                'noOfDaysDD' => $noOfDaysDD,
                'eventWhenDD' => $eventWhenDD,
                'taskCreatedDate' => $taskCreatedDate
            ];
            $tddTimeStampData = getTaskDueDate::getReport($tddParams);
            if ($tddTimeStampData) {
                $taskDueDate = $tddTimeStampData;
                $taskDueDateUI = date('m/d/Y', strtotime($tddTimeStampData));
            }
            //Task Remainder Date
            $taskRemainderDate = '';
            if ($reminderEmail) {
                $trdParams = [
                    'noOfDaysRD' => $noOfDaysRD,
                    'eventWhenRD' => $eventWhenRD,
                    'taskCreatedDate' => $taskCreatedDate,
                    'taskDueDate' => $taskDueDate
                ];
                $trdTimeStampData = getTaskRemainderDate::getReport($trdParams);
                if ($trdTimeStampData != '') {
                    $taskRemainderDate = date('m/d/Y', strtotime($trdTimeStampData));
                }
            }
            $taskRows .= "<tr>
                    <td>";
            if ($userControl == 1 && $cancelAction == 0) {
                $taskRows .= '<label class="checkbox"><input type="checkbox" name="autoTask[]" id="autoTask_' . $t . '" class="autoTask" value="' . $taskId . '" checked><span></span></label>';
            } else {
                $taskRows .= $t;
            }
            $taskRows .= "</td>
                    <td>$taskName</td>
                    <td>$taskType</td>
                    <td>$taskTriggerDate</td>
                    <td>$taskDueDateUI</td>
                    <td>$taskRemainderDate</td>
                    <td>$taskCreatedBy</td>
                    <td>$AssignedUsers</td>
                </tr>";
            $t++;
            ?>
            <?php
        }
        elseif ($actionName == automationConstants::$automation_Email) {
            $showEmailTable = 'yes';
            $emailId = $action['actionId'];
            //get the email Details
            $emparams = ['PCID' => $PCID, 'id' => $emailId];
            $automatedEmail = getAutomatedEmail::getReport($emparams);
            $emailSubject = $automatedEmail['emailSubject'];
            $infoArray['LMRID'] = intval($LMRId);
            LMRequest::$PCID = $PCID;
            $customTempInfoArray = getAllDynamicDatas::getReport($infoArray);
            $customTempInfoArray['DOCCONTENT'] = $emailSubject;
            $customTempInfoArray['LMRID'] = intval($LMRId);
            $subject = SubstituteDynamicTagsForEmail::getReport($customTempInfoArray);
            $subject = str_replace('<b>', '', $subject);
            $subject = str_replace('</b>', '', $subject);
            $emailSubject = $subject;
            $emailType = $automatedEmail['emailType'];
            if ($emailType == 'Schedule') $emailType = 'Scheduled'; //past tense
            $manualEmail = $automatedEmail['manualEmail'];
            $emailCc = $automatedEmail['emailCc'];
            $manualEmailCc = $automatedEmail['manualEmailCc'];
            $emailBcc = $automatedEmail['emailBcc'];
            $manualEmailBcc = $automatedEmail['manualEmailBcc'];
            //Sender
            $missingSenderInfo = '';
            $senderParams = [
                'LMRId' => $LMRId,
                'PCID' => $PCID
            ];
            $emailFromRole = $automatedEmail['emailFromRole'];
            if ($emailFromRole != '') {
                $senderParams['sender'] = 'Role';
                $senderParams['param'] = $emailFromRole; //Role Name
                $missingSenderInfo = $emailFromRole;
            }
            $emailFromEmail = $automatedEmail['emailFromEmail'];
            if ($emailFromEmail != '') {
                $senderParams['sender'] = 'User';
                $senderParams['param'] = $emailFromEmail; //UserId
                $missingSenderInfo = 'File Assigned User';
            }
            $senderDetails = getAutomatedEmailSender::getReport($senderParams, $myFileInfo);
            if (!$senderDetails['fromName'] && !$senderDetails['fromEmail']) {
                $sender = '<span class="text-danger">[Missing info for ' . $missingSenderInfo . ']</span>';
            } else {
                $sender = $senderDetails['fromName'] . ' ' . $senderDetails['fromEmail'] . '[' . $senderDetails['Role'] . ']';
            }

            //Recipients
            //Roles
            $recipients = [];
            $recepRoles = getAutomatedEmailRole::getReport($emparams);
            foreach ($recepRoles as $rr) {
                if (preg_match('/CON_/', $rr['roleName'])) {
                    $recipients[] = str_replace('CON_', '', $rr['roleName']);
                } else {
                    if ($rr['roleName'] == 'Agent') $rr['roleName'] = 'Broker';
                    if ($rr['roleName'] == 'secondaryAgent') $rr['roleName'] = 'Loan Officer';
                    $recipients[] = $rr['roleName'];
                }
            }
            //Users
            $uids = [];
            $recepUserIds = getAutomatedEmailUser::getReport($emparams);
            foreach ($recepUserIds as $ruId) {
                $uids[] = $ruId['userId'];
            }
            if (count($uids) > 0) {
                $id = implode(',', $uids);
                $userParams = ['UID' => $id, 'UType' => 'Employee'];
                $recepUser = getUserInfo::getReport($userParams);
                foreach ($recepUser as $key1 => $value1) {
                    $recipients[] = $value1['employeeNames'];
                }
            }
            //Manual To
            if ($manualEmail) {
                $manualEmail = str_replace('~', ', ', $manualEmail);
            }
            //CC
            if ($emailCc) {
                $emailCcArray = [];
                $emailCcExp = explode('~', $emailCc);
                foreach ($emailCcExp as $ccExp) {
                    if ($ccExp == 'Agent') $ccExp = 'Broker';
                    if ($ccExp == 'secondaryAgent') $ccExp = 'Loan Officer';
                    $emailCcArray[] = $ccExp;
                }
                $emailCc = implode(', ', $emailCcArray);
            }
            if ($manualEmailCc) {
                $manualEmailCc = str_replace('~', ', ', $manualEmailCc);
                $emailCc .= ', ' . $manualEmailCc;
            }
            //BCC
            if ($emailBcc) {
                $emailBccArray = [];
                $emailBccExp = explode('~', $emailBcc);
                foreach ($emailBccExp as $bccExp) {
                    if ($bccExp == 'Agent') $bccExp = 'Broker';
                    if ($bccExp == 'secondaryAgent') $bccExp = 'Loan Officer';
                    $emailBccArray[] = $bccExp;
                }
                $emailBcc = implode(', ', $emailBccArray);
            }
            if ($manualEmailBcc) {
                $manualEmailBcc = str_replace('~', ', ', $manualEmailBcc);
                $emailBcc .= ', ' . $manualEmailBcc;
            }
            //Final Recipients Data
            $recipientsData = '<b>To: </b>';
            if (count($recipients) > 0) $recipientsData .= implode(', ', $recipients);
            if ($manualEmail) $recipientsData .= ', ' . $manualEmail;
            if ($emailCc) $recipientsData .= '<br><b>Cc: </b> ' . $emailCc;
            if ($emailBcc) $recipientsData .= '<br><b>Bcc: </b> ' . $emailBcc;


            if ($emailType == 'Scheduled') {
                $day = $automatedEmail['noOfDays'] == 1 ? ' day ' : ' day(s)';
                $noOfDays = $automatedEmail['noOfDays'];
                $eventWhen = $automatedEmail['eventWhen'];
                if ($automatedEmail['eventWhen'] == 'ON') {
                    $day = '';
                    $noOfDays = ' ON ';
                    $eventWhen = '';
                }
                $emailReferralDateValue = $automatedEmail['referralDate'];
                if ($emailReferralDateValue != '') {
                    $emailReferralDateExplode = explode('-', $emailReferralDateValue);
                    $emailReferralDateCount = count($emailReferralDateExplode);
                    if ($emailReferralDateCount == 1 &&
                        !in_array($emailReferralDateValue, $glEventReferralDateAllFileTypeArrayKeys)) {
                        $emailReferralDateValue = $RDFT . $emailReferralDateValue;
                    }
                }
                $emailReferralDate = $emailReferralDateValue != '' ? $glEventReferralDateArray[$emailReferralDateValue] : '';

                if (trim(strtolower($noOfDays)) == 'on') {
                    $emailSchedule = 'ON , ' . $emailReferralDate;
                } else {
                    $emailSchedule = $noOfDays . $day . ', ' . strtolower($eventWhen) . ', ' . $emailReferralDate;
                }
                //get the referral Date from the Loan file
                $emReferralDateLoanFile = getReferralDateLoanFile::getReport($emailReferralDateValue, $myFileInfo);
                if ($emReferralDateLoanFile) {
                    $schParams = [
                        'noOfDays' => $automatedEmail['noOfDays'],
                        'eventWhen' => $automatedEmail['eventWhen'],
                        'referralDate' => $emReferralDateLoanFile,
                        'userTimeZone' => $userTimeZone
                    ];
                    $emScheduleTimeStampData = getAutomatedActionScheduleTimeStamp::getReport($schParams);
                    if ($emScheduleTimeStampData != '') {
                        $emSchDateTimeStampDe = json_decode($emScheduleTimeStampData);
                        $scheduleOn = $emSchDateTimeStampDe->scheduleDate;
                    }
                }
                if ($emailReferralDate == 'Rule conditions are met') $emailReferralDate = 'Workflow activation ON ';
                if ($scheduleOn) {
                    $emailTriggerDate = ' (' . date('m/d/Y', strtotime($scheduleOn)) . ').';
                } else {
                    $emailTriggerDate = ' [Missing Information for ' . $emailReferralDate . ']';
                    $missingInfo = 1;
                }
                $emailSchedule .= $emailTriggerDate;
            }
            $emailRows .= "<tr>
                    <td>";
            if ($userControl && !$cancelAction) {
                $emailRows .= '<label class="checkbox"><input type="checkbox" name="autoEmail[]" id="autoEmail_' . $e . '" class="autoEmail" value="' . $emailId . '" checked><span></span></label>';
            } else {
                $emailRows .= $e;
            }
            $emailRows .= "</td>
                    <td>$sender</td>
                    <td>$recipientsData</td>
                    <td>$emailSubject</td>
                    <td>$emailType</td>
                    <td>$emailSchedule</td>
                </tr>";
            $e++;
        }
        elseif ($actionName == automationConstants::$automation_Webhook) {
            $showWebhookTable = 'yes';
            $webhookId = $action['actionId'];
            //get the webhook Details
            $whparams = ['PCID' => $PCID, 'id' => $webhookId];
            $automatedWH = getAutomatedWebhook::getReport($whparams);
            $whName = $automatedWH['webhookName'];
            $whType = $automatedWH['webhookType'];
            $whDesc = $automatedWH['webhookDescription'];
            $whURL = $automatedWH['urlNotifiedTo'];
            $webhookSchedule = $dateTimeNow;
            if ($whType == 'Schedule') $whType = 'Scheduled'; //past tense
            if ($whType == 'Scheduled') {
                $day = $automatedWH['noOfDays'] == 1 ? ' day ' : ' day(s)';
                $noOfDays = $automatedWH['noOfDays'];
                $eventWhen = $automatedWH['eventWhen'];
                if ($automatedWH['eventWhen'] == 'ON') {
                    $day = '';
                    $noOfDays = ' ON ';
                    $eventWhen = '';
                }
                $webhookReferralDateValue = $automatedWH['referralDate'];
                if ($webhookReferralDateValue != '') {
                    $webhookReferralDateExplode = explode('-', $webhookReferralDateValue);
                    $webhookReferralDateCount = count($webhookReferralDateExplode);
                    if ($webhookReferralDateCount == 1 &&
                        !in_array($webhookReferralDateValue, $glEventReferralDateAllFileTypeArrayKeys)) { // old value - append file type
                        $webhookReferralDateValue = $RDFT . $webhookReferralDateValue;
                    }
                }
                $webhookReferralDate = $webhookReferralDateValue != '' ? $glEventReferralDateArray[$webhookReferralDateValue] : '';
                if ($automatedTask['eventWhen'] == 'ON') {
                    $webhookSchedule = 'ON , ' . $webhookReferralDate;
                } else {
                    $webhookSchedule = $noOfDays . $day . ', ' . strtolower($eventWhen) . ', ' . $webhookReferralDate;
                }
                //get the referral Date from the Loan file
                $whReferralDateLoanFile = getReferralDateLoanFile::getReport($webhookReferralDateValue, $myFileInfo);
                //pr($referralDateLoanFile);
                if ($whReferralDateLoanFile != '') {
                    $schParams = [
                        'noOfDays' => $automatedWH['noOfDays'],
                        'eventWhen' => $automatedWH['eventWhen'],
                        'referralDate' => $whReferralDateLoanFile,
                        'userTimeZone' => $userTimeZone
                    ];
                    $whScheduleTimeStampData = getAutomatedActionScheduleTimeStamp::getReport($schParams);
                    if ($whScheduleTimeStampData != '') {
                        $whSchDateTimeStampDe = json_decode($whScheduleTimeStampData);
                        $webhookScheduleOn = $whSchDateTimeStampDe->scheduleDate;
                    }
                }
                if ($webhookReferralDate == 'Rule conditions are met') $webhookReferralDate = 'Workflow activation ON ';
                if ($webhookScheduleOn) {
                    $webhookTriggerDate = ' (' . date('m/d/Y', strtotime($webhookScheduleOn)) . ').';
                } else {
                    $webhookTriggerDate = ' [Missing Information for ' . $webhookReferralDate . ']';
                    $missingInfo = 1;
                }
                $webhookSchedule .= $webhookTriggerDate;
            }
            $webhookRows .= "<tr>
                    <td>";
            if ($userControl == 1 && $cancelAction == 0) {
                $webhookRows .= '<label class="checkbox"><input type="checkbox" name="autoWebhook[]" id="autoWebhook_' . $w . '" class="autoWebhook" value="' . $webhookId . '" checked><span></span></label>';
            } else {
                $webhookRows .= $w;
            }
            $webhookRows .= "</td>
                    <td>$whName</td>
                    <td>$whType</td>
                    <td>$whURL</td>
                    <td>$whDesc</td>
                    <td>$webhookSchedule</td>
                <tr>";
            $w++;
        }
        elseif ($actionName === automationConstants::$automation_Employee) {
            $showEmployeeTable = 'yes';
            $employeeId = $action['actionId'];
            $employee = getUserInfo::getReport(['UID' => $employeeId, 'UType' => 'Employee']);
            $employeeNames = $employee[$employeeId]['employeeNames'] ?? '';
            $employeeEmail = $employee[$employeeId]['email'];
            $employeeRows .= "<tr>
                    <td>";
            if ($userControl == 1 && $cancelAction == 0) {
                $employeeRows .= '<label class="checkbox"><input type="checkbox" name="autoEmployee[]" id="autoEmployee_' . $emp . '" class="autoEmployee" value="' . $employeeId . '" checked><span></span></label>';
            } else {
                $employeeRows .= $emp;
            }
            $employeeRows .= "</td>
                    <td>$employeeNames</td>
                    <td>$employeeEmail</td>
                </tr>";
            $emp++;
        }
        elseif ($actionName === automationConstants::$automation_change_file_status) {
            $showChangeFileStatusTable = 'yes';
            $changeFileStatusId = $action['actionId'];
            $params = [
                'PCID' => $PCID
                , 'id' => $changeFileStatusId
                , 'action' => automationConstants::$automation_change_file_status
            ];
            $changeFileStatusDetails = getAutomatedActionTitle::getReport($params);
            $acDeDecode       = json_decode($changeFileStatusDetails);
            $actionTitleName  = $acDeDecode->actionTitle;
            $changeFileStatusRows .= "<tr>
                    <td>";
            if ($userControl == 1 && $cancelAction == 0) {
                $changeFileStatusRows .= '<label class="checkbox"><input type="checkbox" name="autoChangeFileStatus" id="autoChangeFileStatus_' . $cfs . '" class="autoChangeFileStatus" value="' . $changeFileStatusId . '" checked><span></span></label>';
            }
            else {
                $changeFileStatusRows .= $cfs;
            }
            $changeFileStatusRows .= "</td>
                    <td>$actionTitleName</td>
            </tr>";
        }
    }
    $layout .= "<div id='automatedActionViewCss'>";
    $layout .= '<div id="noAutomation" class="alert alert-danger hidden" role="alert">
        Please select at least one automation(s) or click on the Cancel button.
    </div>';
    if ($missingInfo && !$cancelAction) {
        $layout .= '<div class="alert alert-custom alert-light-danger fade show mb-5 missingInfo" role="alert">
    <div class="alert-icon"><i class="flaticon-warning"></i></div>
    <div class="alert-text">You have some missing information that is needed to complete the below actions.
    By hitting cancel and filling in this information you can re-trigger these automations to get them all to run.</div>
</div>';
    }
    if ($cancelAction) {
        $layout .= '<div class="alert alert-custom alert-light-danger fade show mb-5" role="alert">
    <div class="alert-icon"><i class="flaticon-warning"></i></div>
    <div class="alert-text">These automated actions were cancelled.</div>
    <div class="alert-close">
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true"><i class="ki ki-close"></i></span>
        </button>
    </div>
</div>';
    }
    //Task Table
    if ($showTaskTable == 'yes') {
        $layout .= '<div class="clearfix"></div>
        <div class="col-md-12 p-0">
            <div class="col-md-12 pl-0 mb-5"><h5>Automated Tasks to Be Created</h5></div>
            <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead class="thead-light">';
        if ($userControl && !$cancelAction) {
            $layout .= '<th><label class="checkbox" title="Click to check/Uncheck all task(s)">
                    <input type="checkbox" name="taskALL" id="taskALL" class="checkbox" checked><span></span>
                    </label></th>';
        } else {
            $layout .= '<th>#</th>';
        }
        $layout .= '<th>Task Subject</th>
                    <th>Task Type</th>
                    <th>Created Date</th>
                    <th>Due Date</th>
                    <th>Remainder Date</th>
                    <th>Created By</th>
                    <th>Assigned Users</th>
                </thead>
                <tbody>
                ' . $taskRows . '
                </tbody>
            </table>
            </div>
        </div>
        <div class="clearfix"></div>';
    }
    //Email Table
    if ($showEmailTable == 'yes') {
        $layout .= '<div class="clearfix"></div>
        <div class="col-md-12 p-0">
            <div class="col-md-12 pl-0 mb-5"><h5>Automated Emails to Be Created</h5></div>
            <table class="table table-bordered table-hover">
                <thead class="thead-light">';
        if ($userControl && !$cancelAction) {
            $layout .= '<th><label class="checkbox" title="Click to check/Uncheck all email(s)">
            <input type="checkbox" name="emailALL" id="emailALL" class="checkbox" checked><span></span>
            </label></th>';
        } else {
            $layout .= '<th>#</th>';
        }
        $layout .= '<th>From</th>
                <th>Recipient(s)</th>
                <th>Subject</th>
                <th>Email Type</th>
                <th>Send Date</th>
                </thead>
                <tbody>
                    ' . $emailRows . '
                </tbody>
            </table>
        </div>
        <div class="clearfix"></div>';
    }
    //Webhook Table
    if ($showWebhookTable == 'yes') {
        $layout .= '<div class="clearfix"></div>
        <div class="col-md-12 p-0">
            <div class="col-md-12 pl-0 mb-5"><h5>Automated Webhooks to Be Sent</h5></div>
            <table class="table table-bordered table-hover">
                <thead class="thead-light">';
        if ($userControl == 1 && $cancelAction == 0) {
            $layout .= '<th><label class="checkbox" title="Click to check/Uncheck all webhook(s)">
            <input type="checkbox" name="webhookALL" id="webhookALL" class="checkbox" checked><span></span>
            </label></th>';
        } else {
            $layout .= '<th>#</th>';
        }
        $layout .= '<th>Webhook Name</th>
                <th>Webhook Type</th>
                <th>End Point</th>
                <th>Description</th>
                <th>Send Date</th>
                </thead>
                <tbody>
                    ' . $webhookRows . '
                </tbody>
            </table>
        </div>
        <div class="clearfix"></div>';
    }
    //Employee Table
    if ($showEmployeeTable == 'yes') {
        $layout .= '<div class="clearfix"></div>
        <div class="col-md-12 p-0">
            <div class="col-md-12 pl-0 mb-5"><h5>Back Office Employees to Be Assigned</h5></div>
            <table class="table table-bordered table-hover">
                <thead class="thead-light">';
        if ($userControl == 1 && $cancelAction == 0) {
            $layout .= '<th><label class="checkbox" title="Click to check/Uncheck all employee(s)">
            <input type="checkbox" name="employeeALL" id="employeeALL" class="checkbox" checked><span></span>
            </label></th>';
        } else {
            $layout .= '<th>#</th>';
        }
        $layout .= '<th>Employee Name</th>
                <th>Employee Email</th>
                </thead>
                <tbody>
                    ' . $employeeRows . '
                </tbody>
            </table>
        </div>
        <div class="clearfix"></div>';
    }
    //Change File Status Table
    if ($showChangeFileStatusTable == 'yes') {
        $layout .= '<div class="clearfix"></div>
        <div class="col-md-12 p-0">
            <div class="col-md-12 pl-0 mb-5"><h5>Change File Status</h5></div>
            <table class="table table-bordered table-hover">
                <thead class="thead-light">';
        if ($userControl == 1 && $cancelAction == 0) {
            $layout .= '<th><label class="checkbox" title="Click to check/Uncheck change file status">
            <input type="checkbox" name="changeFileStatusALL" id="changeFileStatusALL" class="checkbox" checked><span></span>
            </label></th>';
        } else {
            $layout .= '<th>#</th>';
        }
        $layout .= '<th>Description</th>
                </thead>
                <tbody>
                    ' . $changeFileStatusRows . '
                </tbody>
            </table>
        </div>
        <div class="clearfix"></div>';
    }
    $layout .= '</div>';
}
$layout .= '<style>
.checkbox > span {
    border-color: #3699ff;
}
</style>';
$layout .= "<script>
$('.jconfirm-closeIcon').prop('title', 'Click to close');
$(document).on('click', '#taskALL', function () {
     $('.autoTask').not(this).prop('checked', this.checked);
});
$(document).on('click', '#emailALL', function () {
     $('.autoEmail').not(this).prop('checked', this.checked);
});
$(document).on('click', '#webhookALL', function () {
     $('.autoWebhook').not(this).prop('checked', this.checked);
});
$(document).on('click', '#employeeALL', function () {
     $('.autoEmployee').not(this).prop('checked', this.checked);
});

$(document).on('click', '.autoTask', function () {
    let autoTaskAll = $('input[type=\"checkbox\"][name=\"autoTask[]\"]').length;
    let autoTaskChecked = $('input[type=\"checkbox\"][name=\"autoTask[]\"]:checked').length;
    (autoTaskAll === autoTaskChecked) ? $('#taskALL').prop('checked', true) : $('#taskALL').prop('checked', false);

});
$(document).on('click', '.autoEmail', function () {
    let autoEmailAll = $('input[type=\"checkbox\"][name=\"autoEmail[]\"]').length;
    let autoEmailChecked = $('input[type=\"checkbox\"][name=\"autoEmail[]\"]:checked').length;
    (autoEmailAll === autoEmailChecked) ? $('#emailALL').prop('checked', true) : $('#emailALL').prop('checked', false);

});
$(document).on('click', '.autoWebhook', function () {
    let autoWebhookAll = $('input[type=\"checkbox\"][name=\"autoWebhook[]\"]').length;
    let autoWebhookChecked = $('input[type=\"checkbox\"][name=\"autoWebhook[]\"]:checked').length;
    (autoWebhookAll === autoWebhookChecked) ? $('#webhookALL').prop('checked', true) : $('#webhookALL').prop('checked', false);

});
$(document).on('click', '.autoEmployee', function () {
    let autoEmployeeAll = $('input[type=\"checkbox\"][name=\"autoEmployee[]\"]').length;
    let autoEmployeeChecked = $('input[type=\"checkbox\"][name=\"autoEmployee[]\"]:checked').length;
    (autoEmployeeAll === autoEmployeeChecked) ? $('#employeeALL').prop('checked', true) : $('#employeeALL').prop('checked', false);

});
</script>";
echo $layout;
